const http = require('http');

const data = JSON.stringify({
  prompt: "Test Business Analysis for debugging storage",
  context: "This is a test to verify that Business Analysis results are being stored properly.",
  metadata: {
    source: "PMO",
    pmoId: "test-storage-" + Date.now(),
    projectTitle: "Storage Debug Test Project",
    projectDescription: "Testing Business Analysis storage",
    priority: "High",
    autoTriggered: false
  },
  category: "Test Category"
});

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/business-analysis-agent-collaboration',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length
  }
};

console.log('Sending Business Analysis request for storage debugging...');

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    try {
      const result = JSON.parse(responseData);
      console.log('Response:', JSON.stringify(result, null, 2));
    } catch (e) {
      console.log('Raw response:', responseData);
    }
  });
});

req.on('error', (e) => {
  console.error('Error:', e);
});

req.write(data);
req.end();
