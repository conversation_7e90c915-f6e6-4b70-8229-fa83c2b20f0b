const http = require('http');

const data = JSON.stringify({
  prompt: "Test Business Analysis for timestamp debugging",
  context: "This is a test to verify that timestamps are properly stored in Firestore.",
  metadata: {
    source: "PMO",
    pmoId: "test-timestamp-" + Date.now(),
    projectTitle: "Timestamp Test Project",
    projectDescription: "Testing timestamp storage",
    priority: "High",
    autoTriggered: false
  },
  category: "Test Category"
});

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/business-analysis-agent-collaboration',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length
  }
};

console.log('Sending Business Analysis request...');

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    try {
      const result = JSON.parse(responseData);
      console.log('Response:', result);
    } catch (e) {
      console.log('Raw response:', responseData);
    }
  });
});

req.on('error', (e) => {
  console.error('Error:', e);
});

req.write(data);
req.end();
