import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { BusinessAnalysisAgentManager, BusinessAnalysisType, PMOBusinessAnalysisRequest } from '../../../lib/agents/ba/BusinessAnalysisAgentManager';
import { AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';
// adminDb will be imported dynamically to match Investigative Research pattern

// Helper function to remove undefined values for Firestore
function removeUndefinedValues(obj: any): any {
  if (obj === null || obj === undefined) {
    return null;
  }

  // --- FIX APPLIED HERE ---
  // Check if the object is a Firestore Timestamp. If so, return it directly.
  // This prevents the function from recursively turning it into a plain object.
  // The `toDate` method is a reliable way to identify a Timestamp.
  if (typeof obj === 'object' && typeof obj.toDate === 'function') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(removeUndefinedValues).filter(item => item !== undefined);
  }
  
  if (typeof obj === 'object') {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const cleanedValue = removeUndefinedValues(value);
      if (cleanedValue !== undefined) {
        cleaned[key] = cleanedValue;
      }
    }
    return cleaned;
  }
  
  return obj;
}

/**
 * Business Analysis Agent Collaboration API
 *
 * This endpoint provides a standardized interface for PMO-to-Business Analysis team communication.
 * It processes PMO requests and initiates Business Analysis team workflows automatically.
 */
export async function POST(request: NextRequest) {
  try {
    // Handle authentication - support both session-based and internal API authentication
    let userId: string;

    // Check for internal API authentication first
    const internalAuthHeader = request.headers.get('X-Internal-Auth');
    const internalSecretFromEnv = process.env.INTERNAL_API_SECRET;

    if (internalAuthHeader) {
      // Internal API authentication (server-to-server calls)
      if (!internalSecretFromEnv || internalSecretFromEnv.trim() === '') {
        console.error('INTERNAL_API_SECRET environment variable is not configured properly for business analysis API.');
        return NextResponse.json(
          { success: false, error: 'Internal authentication is not configured correctly on the server.' },
          { status: 500 }
        );
      }

      if (internalAuthHeader.trim() === internalSecretFromEnv.trim()) {
        const actingAsUserId = request.headers.get('X-Acting-As-User-Id');
        if (actingAsUserId && actingAsUserId.trim() !== '') {
          userId = actingAsUserId.trim();
          console.log(`[Business Analysis API] Internal authentication successful. Acting as user: ${userId}`);
        } else {
          userId = 'system-default-user';
          console.log(`[Business Analysis API] Internal authentication successful. No user ID provided, using system default.`);
        }
      } else {
        console.warn('[Business Analysis API] Internal call attempt with invalid X-Internal-Auth secret.');
        return NextResponse.json(
          { success: false, error: 'Invalid internal authentication credentials.' },
          { status: 401 }
        );
      }
    } else {
      // Standard session-based authentication
      const session = await getServerSession(authOptions);
      if (!session?.user?.email) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }
      userId = session.user.email;
    }
    const requestBody = await request.json();

    console.log('[Business Analysis API] Processing business analysis collaboration request:', {
      userId,
      hasPrompt: !!requestBody.prompt,
      hasPMOContext: !!requestBody.metadata?.pmoId
    });

    // Validate required fields
    const { prompt } = requestBody;

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Determine if this is a PMO task or direct business analysis request
    const isPMOTask = requestBody.metadata?.source === 'PMO' && requestBody.metadata?.pmoId;

    let result;

    if (isPMOTask) {
      // Process as PMO task using BusinessAnalysisAgentManager
      result = await processPMOTask(requestBody, userId);
    } else {
      // Process as direct business analysis request
      result = await processDirectBusinessAnalysisRequest(requestBody, userId);
    }

    return NextResponse.json({
      success: result.success,
      requestId: result.requestId,
      output: result.output,
      metadata: result.metadata,
      error: result.error
    });

  } catch (error) {
    console.error('[Business Analysis API] Error processing request:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

/**
 * Process PMO task using BusinessAnalysisAgentManager
 */
async function processPMOTask(requestBody: any, userId: string) {
  const { prompt, context, metadata } = requestBody;

  // Create PMO business analysis request
  const pmoRequest: PMOBusinessAnalysisRequest = {
    pmoId: metadata.pmoId,
    title: metadata.projectTitle || metadata.recordTitle || prompt,
    description: context || metadata.projectDescription || '',
    analysisType: determineAnalysisType(prompt, context),
    selectedAnalystIds: [], // Will use intelligent selection
    primaryModel: 'claude-sonnet-4-0',
    fallbackModel: 'deepseek-r1',
    validationModel: 'claude-sonnet-4-0',
    userId,
    priority: metadata.priority || 'Medium',
    enableSystemAnalysis: true,
    enableRequirementsEngineering: true,
    enableSpecificationDevelopment: true,
    documentCategory: metadata.category || requestBody.category
  };

  // Initialize BusinessAnalysisAgentManager
  const baManager = new BusinessAnalysisAgentManager({
    userId,
    defaultLlmProvider: 'anthropic',
    defaultLlmModel: 'claude-sonnet-4-0'
  });

  // Conduct the business analysis
  const result = await baManager.conductPMOBusinessAnalysis(pmoRequest);

  // Store the business analysis output in global Firestore collection for PMO integration
  if (result && result.consolidatedAnalysis) {
    try {
      const { v4: uuidv4 } = await import('uuid');
      const requestId = uuidv4();

      // Extract PMO metadata for proper PMO Output Tab integration
      const recordTitle = metadata.projectTitle || metadata.recordTitle || 'Business Analysis';
      const projectTitle = metadata.projectTitle || recordTitle;

      // Import Firebase admin dynamically like Investigative Research agent
      const { adminDb } = await import('../../../components/firebase-admin');
      console.log(`[BA_AGENT_OUTPUT] Firebase admin imported successfully`);

      // Use actual completion timestamp from result
      const completionTimestamp = result.timestamp || new Date();

      // Import Firestore admin for proper timestamp handling (matching Investigative Research pattern)
      const { Timestamp } = await import('firebase-admin/firestore');
      const firestoreTimestamp = Timestamp.fromDate(completionTimestamp);

      console.log(`[BA_AGENT_OUTPUT] Created Firestore timestamp for: ${completionTimestamp}`);

      // Prepare data to be stored in global Firestore collection (matching Investigative Research pattern)
      const agentOutputData = {
        requestId,
        timestamp: firestoreTimestamp,
        createdAt: firestoreTimestamp,
        agentType: 'BusinessAnalysis',
        userId,
        prompt: requestBody.prompt,
        result: {
          thinking: `Multi-analyst business analysis using ${result.analystResponses.length} specialized perspectives`,
          output: result.consolidatedReport || result.consolidatedAnalysis,
          documentUrl: null
        },
        agentMessages: [],
        modelInfo: {
          provider: 'multi-llm',
          model: 'consolidated-business-analysis'
        },
        contextOptions: {
          customContext: requestBody.context || null,
          documentReferences: requestBody.documentReferences || null,
          category: requestBody.category || null
        },
        metadata: {
          source: 'Business Analysis Team',
          pmoId: metadata.pmoId,
          recordTitle,
          recordDescription: metadata.projectDescription || context || '',
          recordPriority: metadata.priority || 'Medium',
          teamId: AgenticTeamId.BusinessAnalysis,
          analysisType: result.analysisType,
          analystCount: result.analystResponses.length,
          duration: result.duration,
          modelConfiguration: result.metadata.modelConfiguration,

          processedAt: completionTimestamp.toISOString(),
          autoTriggered: metadata.autoTriggered || false,
          triggerTimestamp: metadata.triggerTimestamp,
          teamSelectionRationale: metadata.teamSelectionRationale,
          pmoAssessment: metadata.pmoAssessment,
          requirementsDocument: metadata.requirementsDocument
        }
      };

      // Timestamps are now properly created as Firestore Timestamp objects

      // Clean the data to remove undefined values (Firestore doesn't allow undefined)
      const cleanedData = removeUndefinedValues(agentOutputData);

      console.log(`[BA_AGENT_OUTPUT] Storing business analysis output with requestId: ${requestId}`);
      await adminDb.collection('Agent_Output').doc(requestId).set(cleanedData);
      console.log(`[BA_AGENT_OUTPUT] Successfully stored business analysis output with requestId: ${requestId}`);

      // Add the requestId to the result for reference
      (result as any).requestId = requestId;
    } catch (storageError) {
      console.error(`[BA_AGENT_OUTPUT] Error storing business analysis output:`, storageError);
      // Continue with the response even if storage fails
    }
  }

  return {
    success: true,
    requestId: (result as any).requestId,
    output: result.consolidatedReport || result.consolidatedAnalysis,
    metadata: {
      source: 'Business Analysis Team',
      pmoId: metadata.pmoId,
      processedAt: (result.timestamp || new Date()).toISOString(),
      teamId: AgenticTeamId.BusinessAnalysis,
      analysisType: result.analysisType,
      analystCount: result.analystResponses.length,
      duration: result.duration
    },
    error: null
  };
}

/**
 * Process direct business analysis request
 */
async function processDirectBusinessAnalysisRequest(requestBody: any, userId: string) {
  const { prompt, context, metadata } = requestBody;

  // Initialize BusinessAnalysisAgentManager
  const baManager = new BusinessAnalysisAgentManager({
    userId,
    defaultLlmProvider: 'anthropic',
    defaultLlmModel: 'claude-sonnet-4-0'
  });

  // Create direct analysis request
  const analysisRequest = {
    prompt,
    context,
    analysisType: determineAnalysisType(prompt, context),
    selectedAnalysts: [], // Will use intelligent selection
    primaryModel: 'claude-sonnet-4-0',
    fallbackModel: 'deepseek-r1',
    userId,
    enableSystemAnalysis: true,
    enableRequirementsEngineering: true,
    enableSpecificationDevelopment: true,
    documentCategory: requestBody.category
  };

  // Create PMO request for direct analysis
  const pmoRequest: PMOBusinessAnalysisRequest = {
    pmoId: 'direct-request',
    title: prompt,
    description: context || '',
    analysisType: analysisRequest.analysisType,
    selectedAnalystIds: [],
    primaryModel: 'claude-sonnet-4-0',
    fallbackModel: 'deepseek-r1',
    userId,
    priority: 'Medium',
    enableSystemAnalysis: true,
    enableRequirementsEngineering: true,
    enableSpecificationDevelopment: true,
    documentCategory: requestBody.category
  };

  // Conduct the business analysis
  const result = await baManager.conductPMOBusinessAnalysis(pmoRequest);

  // Generate output summary
  const completionTimestamp = result.timestamp || new Date();
  const output = `# Business Analysis Report

## Analysis Type: ${result.analysisType}
## Analysts: ${result.analystResponses.length}
## Duration: ${Math.round(result.duration / 1000)}s

${result.consolidatedAnalysis}

---
*Generated by Business Analysis Team - ${completionTimestamp.toISOString()}*`;

  return {
    success: true,
    requestId: result.analysisId,
    output,
    metadata: {
      source: 'Business Analysis Team',
      processedAt: completionTimestamp.toISOString(),
      teamId: AgenticTeamId.BusinessAnalysis,
      analysisType: result.analysisType,
      analystCount: result.analystResponses.length,
      duration: result.duration
    },
    error: null
  };
}

/**
 * Determine analysis type from prompt and context
 */
function determineAnalysisType(prompt: string, context?: string): BusinessAnalysisType {
  const combined = `${prompt} ${context || ''}`.toLowerCase();

  if (combined.includes('system') || combined.includes('architecture') || combined.includes('component')) {
    return BusinessAnalysisType.SYSTEM_ANALYSIS;
  } else if (combined.includes('product') || combined.includes('vision') || combined.includes('strategy')) {
    return BusinessAnalysisType.PRODUCT_OVERVIEW;
  } else if (combined.includes('requirement') || combined.includes('specification') || combined.includes('functional')) {
    return BusinessAnalysisType.REQUIREMENTS_ENGINEERING;
  } else if (combined.includes('use case') || combined.includes('test case') || combined.includes('specification')) {
    return BusinessAnalysisType.SPECIFICATION_DEVELOPMENT;
  } else {
    return BusinessAnalysisType.COMPREHENSIVE;
  }
}

/**
 * GET endpoint for business analysis team capabilities
 */
export async function GET(req: NextRequest) {
  try {
    // Return business analysis team capabilities and status
    const capabilities = {
      teamId: AgenticTeamId.BusinessAnalysis,
      teamName: 'Business Analysis Team',
      capabilities: [
        'System architecture and component analysis',
        'Product overview and strategic positioning',
        'Requirements engineering and documentation',
        'Functional specifications development',
        'Use case modeling and test case generation',
        'Cross-functional business analysis',
        'Strategic implementation planning'
      ],
      supportedAnalysisTypes: [
        'system-analysis',
        'product-overview',
        'requirements-engineering',
        'specification-development',
        'comprehensive'
      ],
      pmoIntegration: {
        enabled: true,
        features: [
          'PMO task processing',
          'Strategic implementation planning',
          'Cross-team coordination',
          'PMO document standards compliance'
        ]
      },
      apiVersion: '1.0.0',
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: capabilities
    });

  } catch (error) {
    console.error('Error fetching business analysis capabilities:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}