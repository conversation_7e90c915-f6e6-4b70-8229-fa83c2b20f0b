// Test script to trigger a Business Analysis and check timestamp storage
const fetch = require('node-fetch');

async function testBusinessAnalysis() {
  try {
    console.log('Testing Business Analysis timestamp storage...');
    
    const response = await fetch('http://localhost:3001/api/business-analysis-agent-collaboration', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'Test Business Analysis for timestamp debugging',
        context: 'This is a test to verify that timestamps are properly stored in Firestore.',
        metadata: {
          source: 'PMO',
          pmoId: 'test-timestamp-' + Date.now(),
          projectTitle: 'Timestamp Test Project',
          projectDescription: 'Testing timestamp storage',
          priority: 'High',
          autoTriggered: false
        },
        category: 'Test Category'
      })
    });

    const result = await response.json();
    console.log('Business Analysis Response:', result);
    
    if (result.success) {
      console.log('✅ Business Analysis completed successfully');
      console.log('Request ID:', result.requestId);
      console.log('Check the console logs for timestamp debug information');
    } else {
      console.log('❌ Business Analysis failed:', result.error);
    }
    
  } catch (error) {
    console.error('Error testing Business Analysis:', error);
  }
}

testBusinessAnalysis();
